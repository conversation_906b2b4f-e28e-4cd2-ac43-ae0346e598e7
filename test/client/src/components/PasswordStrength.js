import React, { useState } from 'react';
import {
  <PERSON>, Card<PERSON>ontent, <PERSON>po<PERSON>, TextField, Button, Alert, Stack
} from '@mui/material';

function PasswordStrength() {
  const [password, setPassword] = useState('');
  const [strength, setStrength] = useState('');

  const checkStrength = () => {
    if (password.length < 6) {
      setStrength('Weak password');
    } else if (/\d/.test(password)) {
      setStrength('Strong password');
    } else {
      setStrength('Needs at least one number');
    }
  };

  return (
    <Card sx={{ maxWidth: 400, margin: 'auto', mt: 4, p: 2, boxShadow: 3 }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Password Strength Checker
        </Typography>
        <Stack spacing={2}>
          <TextField
            label="Enter Password"
            type="password"
            fullWidth
            value={password}
            onChange={(e) => setPassword(e.target.value)}
          />
          <Button variant="contained" onClick={checkStrength}>Check Strength</Button>
          {strength && (
            <Alert severity={
              strength === 'Strong password' ? 'success' : 'warning'
            }>
              {strength}
            </Alert>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
}

export default PasswordStrength;
