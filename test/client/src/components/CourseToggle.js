import React, { useState } from 'react';
import {
  Card,
  Card<PERSON>ontent,
  Typography,
  Button,
  Collapse,
  Box,
} from '@mui/material';

function CourseToggle() {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <Card variant="outlined" sx={{ borderRadius: 2, boxShadow: 3 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Toggle Course Description
        </Typography>

        <Button
          variant="contained"
          color={isVisible ? 'error' : 'primary'}
          onClick={() => setIsVisible(!isVisible)}
        >
          {isVisible ? 'Hide Description' : 'Show Description'}
        </Button>

        <Collapse in={isVisible}>
          <Box mt={2}>
            <Typography variant="body1">
              This course covers React fundamentals including components, JSX, and props.
            </Typography>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
}

export default CourseToggle;
