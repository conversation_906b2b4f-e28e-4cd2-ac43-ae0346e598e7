import React, { useState } from 'react';
import {
  Card,
  TextField,
  Button,
  Typography,
  Box,
  CardContent,
  CardActions
} from '@mui/material';
import { motion } from 'framer-motion';

// Login Component
function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [message, setMessage] = useState('');

  const handleLogin = (e) => {
    e.preventDefault();
    if (!email || !password) {
      setMessage('Please fill in all fields.');
    } else if (!email.includes('@')) {
      setMessage('Invalid email format.');
    } else {
      setMessage('Login successful!');
    }
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
      <Card sx={{ maxWidth: 400, margin: 'auto', mt: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Login to LMS
          </Typography>
          <Box component="form" onSubmit={handleLogin}>
            <TextField
              label="Email"
              fullWidth
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              margin="normal"
            />
            <TextField
              label="Password"
              type="password"
              fullWidth
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              margin="normal"
            />
            <CardActions>
              <Button variant="contained" fullWidth type="submit">
                Login
              </Button>
            </CardActions>
            {message && (
              <Typography mt={2} color={message.includes('success') ? 'green' : 'error'}>
                {message}
              </Typography>
            )}
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
}

// Course Recommender Component
function CourseRecommender() {
  const [interest, setInterest] = useState('');
  const [recommended, setRecommended] = useState('');

  const recommendCourse = () => {
    if (interest.toLowerCase().includes('web')) {
      setRecommended('We recommend: React.js for Beginners');
    } else if (interest.toLowerCase().includes('data')) {
      setRecommended('We recommend: Intro to Data Science with Python');
    } else if (interest.toLowerCase().includes('ai')) {
      setRecommended('We recommend: Machine Learning with Scikit-Learn');
    } else {
      setRecommended('Please enter a valid interest (e.g., AI, Web, Data)');
    }
  };

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
      <Card sx={{ maxWidth: 500, margin: 'auto', mt: 4 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            AI Course Recommender
          </Typography>
          <TextField
            label="Your Interest"
            placeholder="e.g., AI, Web, Data"
            fullWidth
            value={interest}
            onChange={(e) => setInterest(e.target.value)}
            margin="normal"
          />
          <CardActions>
            <Button variant="contained" onClick={recommendCourse}>
              Get Recommendation
            </Button>
          </CardActions>
          {recommended && (
            <Typography mt={2} color="primary">
              {recommended}
            </Typography>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}

export { Login, CourseRecommender };
