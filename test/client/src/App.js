import React from 'react';
import {
  Container,
  Typography,
  Divider,
  Box,
  CssBaseline,
  Paper,
} from '@mui/material';
import { Lo<PERSON>, CourseRecommender } from './components/LMSComponents';
import PasswordStrength from './components/PasswordStrength';
import CourseToggle from './components/CourseToggle';
import { motion } from 'framer-motion';

const sectionVariant = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0 },
};

function App() {
  return (
    <>
      <CssBaseline />
      <Container maxWidth="sm">
        <Box my={4} textAlign="center">
          <Typography variant="h3" component="h1" gutterBottom>
            AI LMS React App
          </Typography>
          <Typography variant="subtitle1" color="textSecondary">
            Learn React by building interactive components
          </Typography>
        </Box>

        {/* Section: Login */}
        <motion.div
          variants={sectionVariant}
          initial="hidden"
          animate="visible"
          transition={{ duration: 0.5 }}
        >
          <Divider sx={{ mb: 2 }}>Login</Divider>
          <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
            <Login />
          </Paper>
        </motion.div>

        {/* Section: Password Strength */}
        <motion.div
          variants={sectionVariant}
          initial="hidden"
          animate="visible"
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Divider sx={{ mb: 2 }}>Password Strength Checker</Divider>
          <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
            <PasswordStrength />
          </Paper>
        </motion.div>

        {/* Section: Course Recommender */}
        <motion.div
          variants={sectionVariant}
          initial="hidden"
          animate="visible"
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Divider sx={{ mb: 2 }}>Course Recommender</Divider>
          <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
            <CourseRecommender />
          </Paper>
        </motion.div>

        {/* Section: Course Toggle */}
        <motion.div
          variants={sectionVariant}
          initial="hidden"
          animate="visible"
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Divider sx={{ mb: 2 }}>Course Description Toggle</Divider>
          <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
            <CourseToggle />
          </Paper>
        </motion.div>
      </Container>
    </>
  );
}

export default App;
