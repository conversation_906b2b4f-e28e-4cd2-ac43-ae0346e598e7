1 // Section 3: Database - MongoDB
2
3 // Used ADD DATA --> Insert document
4
5 //data schools
6 [
7 {
8 "_id": { "$oid": "665f1fa4a7d3f1a0aabc1001" },
9 "name": "Greenwood High School",
10 "address": "123 Maple Street, Springfield",
11 "principal": "Mr. <PERSON>"
12 },
13 {
14 "_id": { "$oid": "665f1fa4a7d3f1a0aabc1002" },
15 "name": "Riverside Public School",
16 "address": "456 Oak Avenue, Riverdale",
17 "principal": "<PERSON><PERSON>"
18 }
19 ]
20
21 //data courses
22 [
23 {
24 "_id": { "$oid": "665f1fc4a7d3f1a0aabc2001" },
25 "title": "Mathematics",
26 "description": "Basic algebra and geometry",
27 "schoolId": { "$oid": "665f1fa4a7d3f1a0aabc1001" }
28 },
29 {
30 "_id": { "$oid": "665f1fc4a7d3f1a0aabc2002" },
31 "title": "Science",
32 "description": "Introduction to Physics and Chemistry",
33 "schoolId": { "$oid": "665f1fa4a7d3f1a0aabc1002" }
34 }
35 ]
36
37 //data enrollments
38 [
39 {
40 "_id": { "$oid": "665f1fd1a7d3f1a0aabc3001" },
41 "studentName": "Alice Nguyen",
42 "courseId": { "$oid": "665f1fc4a7d3f1a0aabc2001" },
43 "enrolledDate": "2025-06-29T00:00:00Z"
44 },
45 {
46 "_id": { "$oid": "665f1fd1a7d3f1a0aabc3002" },
47 "studentName": "Bob Tran",
48 "courseId": { "$oid": "665f1fc4a7d3f1a0aabc2002" },
49 "enrolledDate": "2025-06-29T00:00:00Z"
50 }
51 ]
52
