Capstone Project - Section 3: SQL with MySQL
Objective
This section teaches you how to use MySQL to build the backend of a Learning Management System (LMS).
You'll create databases, tables, insert data, and perform CRUD operations.
Step 1: Access MySQL via Command Prompt

- Open Command Prompt (as Administrator).
- Navigate to MySQL bin folder:
  cd "C:\Program Files\MySQL\MySQL Server 8.0\bin"
- Login: mysql -u root -p
  Step 2: Create Database and Tables
  CREATE DATABASE lms_db;
  USE lms_db;
  -- Users Table
  CREATE TABLE users (
  user_id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100),
  email VARCHAR(100) UNIQUE,
  password VARCHAR(255)
  );
  -- Courses Table
  CREATE TABLE courses (
  course_id INT AUTO_INCREMENT PRIMARY KEY,
  course_name VARCHAR(100),
  description TEXT
  );
  Capstone Project - Section 3: SQL with MySQL
  -- Enrollments Table
  CREATE TABLE enrollments (
  enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT,
  course_id INT,
  enrollment_date DATE,
  FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(user_id),
  FOREIGN KEY (course_id) REFERENCES courses(course_id)
  );
  -- Assessments Table
  CREATE TABLE assessments (
  assessment_id INT AUTO_INCREMENT PRIMARY KEY,
  course_id INT,
  title VARCHAR(100),
  max_score INT,
  FOREIGN KEY (course_id) REFERENCES courses(course_id)
  );
  Step 3: Insert Sample Data
  -- Users
  INSERT INTO users (name, email, password) VALUES
  ('Alice Johnson', '<EMAIL>', 'alice123'),
  ('Bob Smith', '<EMAIL>', 'bob123'),
  ('Charlie Lee', '<EMAIL>', 'charlie123');
  -- Courses
  INSERT INTO courses (course_name, description) VALUES
  ('HTML Basics', 'Introduction to HTML and web structure.'),
  ('CSS Design', 'Learn how to style websites using CSS.'),
  Capstone Project - Section 3: SQL with MySQL
  ('MySQL for Beginners', 'Basic concepts of relational databases.');
  -- Enrollments
  INSERT INTO enrollments (user_id, course_id, enrollment_date) VALUES
  (1, 1, '2024-01-10'),
  (1, 3, '2024-02-05'),
  (2, 2, '2024-02-15'),
  (3, 1, '2024-03-01');
  -- Assessments
  INSERT INTO assessments (course_id, title, max_score) VALUES
  (1, 'HTML Quiz 1', 100),
  (2, 'CSS Midterm', 80),
  (3, 'MySQL Final Test', 90);
  Step 4: Perform CRUD Operations
  -- Read All Users
  SELECT \* FROM users;
  -- Enrollments for a user
  SELECT u.name, c.course_name FROM enrollments e
  JOIN users u ON e.user_id = u.user_id
  JOIN courses c ON e.course_id = c.course_id
  WHERE u.user_id = 1;
  -- Update User
  UPDATE users SET email = '<EMAIL>' WHERE user_id = 1;
  -- Delete Enrollment
  DELETE FROM enrollments WHERE enrollment_id = 2;
  Capstone Project - Section 3: SQL with MySQL
  Task: Add Instructors Table
  CREATE TABLE instructors (
  instructor_id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100),
  email VARCHAR(100) UNIQUE
  );
  -- Insert sample instructor
  INSERT INTO instructors (name, email) VALUES ('Marcus Smart', '<EMAIL>');
  Task: Add New User & Enroll
  -- Add Daniel Rose
  INSERT INTO users (name, email, password) VALUES ('Daniel Rose', '<EMAIL>', 'daniel123');
  -- Find course_id of CSS Design
  SELECT course_id FROM courses WHERE course_name = 'CSS Design';
  -- Enroll Daniel (assuming course_id = 2)
  INSERT INTO enrollments (user_id, course_id, enrollment_date) VALUES (4, 2, CURDATE());
  Task: Show All Users Enrolled in CSS Design
  SELECT u.name FROM enrollments e
  JOIN users u ON e.user_id = u.user_id
  JOIN courses c ON e.course_id = c.course_id
  WHERE c.course_name = 'CSS Design';
