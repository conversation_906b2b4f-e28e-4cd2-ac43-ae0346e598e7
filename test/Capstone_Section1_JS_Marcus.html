<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>🌟 AI LMS - JavaScript Interactivity</title>
    <style>
        :root {
            --primary: #1a73e8;
            --error: #e53935;
            --success: #2e7d32;
            --bg: #f0f4f8;
            --white: #fff;
            --text: #333;
            --radius: 12px;
            --shadow: 0 8px 18px rgba(0, 0, 0, 0.1);
        }

        * {
            box-sizing: border-box;
        }

        body {
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background-color: var(--bg);
            color: var(--text);
            padding: 40px 20px;
            animation: fadeInBody 1s ease-out;
        }

        @keyframes fadeInBody {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h1 {
            text-align: center;
            margin-bottom: 50px;
            color: var(--primary);
            font-size: 2.2rem;
            animation: popIn 0.6s ease-out;
        }

        @keyframes popIn {
            0% {
                transform: scale(0.95);
                opacity: 0;
            }

            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .section {
            background: var(--white);
            padding: 25px;
            margin: 20px auto;
            max-width: 600px;
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            animation: slideUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(20px);
        }

        .section:nth-child(2) {
            animation-delay: 0.3s;
        }

        .section:nth-child(3) {
            animation-delay: 0.5s;
        }

        .section:nth-child(4) {
            animation-delay: 0.7s;
        }

        .section:nth-child(5) {
            animation-delay: 0.9s;
        }

        @keyframes slideUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h2 {
            margin-top: 0;
            color: var(--primary);
            font-size: 1.3rem;
        }

        input,
        button {
            width: 100%;
            padding: 12px 16px;
            font-size: 1rem;
            border-radius: var(--radius);
            margin-top: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            transition: all 0.3s ease;
        }

        input:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.2);
        }

        button {
            background: var(--primary);
            color: white;
            font-weight: bold;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        button::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-100%);
            transition: transform 0.4s;
        }

        button:hover::after {
            transform: translateX(100%);
        }

        button:hover {
            background-color: #1664c4;
        }

        .msg {
            font-weight: 600;
            margin-top: 5px;
            opacity: 0;
            animation: fadeInMsg 0.5s ease forwards;
        }

        @keyframes fadeInMsg {
            from {
                opacity: 0;
                transform: scale(0.95);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .msg.success {
            color: var(--success);
        }

        .msg.error {
            color: var(--error);
        }

        input:focus::placeholder {
            color: transparent;
        }

        #goalOutput {
            font-style: italic;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        input:focus+#goalOutput {
            opacity: 1;
            font-weight: bold;
            color: var(--primary);
        }
    </style>
</head>

<body>

    <h1>✨ AI LMS: JavaScript Practice</h1>

    <section class="section">
        <h2>1. Enroll in Course</h2>
        <button id="btnEnroll">Click to Enroll</button>
    </section>

    <section class="section">
        <h2>2. AI Suggestion Box</h2>
        <p id="suggestionText">Click the button below to get a course suggestion.</p>
        <button id="btnSuggest">Get AI Suggestion</button>
    </section>

    <section class="section">
        <h2>3. Validate Learner Email</h2>
        <form id="emailForm" novalidate>
            <input id="email" placeholder="📧 Enter your email" />
            <button type="submit">Submit</button>
        </form>
        <p id="emailMsg" class="msg"></p>
    </section>

    <section class="section">
        <h2>4. Type Your Learning Goal</h2>
        <input id="goalInput" placeholder="💡 e.g., Master JavaScript…" />
        <p id="goalOutput">Your goal:</p>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('btnEnroll')
                .addEventListener('click', () =>
                    alert("🎉 You've been enrolled in 'JavaScript Essentials'!"));

            const suggestionText = document.getElementById('suggestionText');
            document.getElementById('btnSuggest')
                .addEventListener('click', () => {
                    suggestionText.textContent = "We recommend: 'Responsive Web Design' next!";
                    suggestionText.style.color = "var(--primary)";
                });

            const emailInput = document.getElementById('email');
            const emailMsg = document.getElementById('emailMsg');
            document.getElementById('emailForm')
                .addEventListener('submit', (e) => {
                    e.preventDefault();
                    const email = emailInput.value.trim();
                    const isValid = /.+@.+\..+/.test(email);

                    emailMsg.textContent = isValid ? "✅ Email accepted!" : "❌ Invalid email address";
                    emailMsg.className = `msg ${isValid ? 'success' : 'error'}`;
                });

            const goalInput = document.getElementById('goalInput');
            const goalOutput = document.getElementById('goalOutput');

            goalInput.addEventListener('input', () => {
                goalOutput.textContent = "Your goal: " + goalInput.value;
            });
        });
    </script>
</body>

</html>