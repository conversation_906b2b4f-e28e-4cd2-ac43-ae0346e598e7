Capstone Section 4 – Smart Search: Reflection
Answers

1. Smart Search lifts the search bar from a simple text matcher into a mentor-like guide. By
   recognizing synonyms, partial terms, and common misspellings, it surfaces relevant courses instantly,
   reducing frustration and discovery time so learners stay in flow rather than guessing exact titles.
2. On the client, a React component debounces keystrokes, calling an API with the evolving query. The
   server endpoint parses that text, runs NLP or keyword algorithms against indexed course metadata in
   MongoDB, ranks matches, and returns JSON. The UI then renders suggestions immediately, closing
   the loop.
3. Developers must balance speed, accuracy, and complexity. Large NLP models can slow response or
   strain resources, while naïve keyword lists miss intent. Caching, throttling, and lightweight vector
   embeddings mitigate latency; continuous feedback loops and curated synonym dictionaries improve
   relevance without over-engineering.
